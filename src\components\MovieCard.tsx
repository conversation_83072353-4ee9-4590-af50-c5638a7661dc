import { useState } from 'react'
import { Link } from 'react-router-dom'
import type { Movie } from '../types/movie'
import { getImageUrl, createPlaceholderImage } from '../config/env'

interface MovieCardProps {
  movie: Movie
}

const MovieCard = ({ movie }: MovieCardProps) => {
  const [isHovered, setIsHovered] = useState(false)

  // Get rating from TMDB or default
  const getRating = () => {
    return movie.tmdb?.vote_average ? movie.tmdb.vote_average.toFixed(1) : '0.0'
  }

  return (
    <div
      className="relative flex-shrink-0 w-72 group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Movie Poster */}
      <div className="relative overflow-hidden rounded-lg bg-gray-800">
        <img
          src={getImageUrl(movie.poster_url || movie.thumb_url)}
          alt={movie.name}
          className="w-full h-96 object-cover transition-transform duration-300 group-hover:scale-110"
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.src = createPlaceholderImage(300, 450, 'No Image')
          }}
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Rating Badge */}
        <div className="absolute top-3 left-3 bg-black/80 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-sm font-semibold flex items-center space-x-1">
          <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
          <span>{getRating()}</span>
        </div>

        {/* Play Button - Shows on Hover */}
        <div className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <Link
            to={`/movie/${movie.slug}`}
            className="bg-red-600 hover:bg-red-700 text-white p-4 rounded-full transition-colors transform hover:scale-110"
          >
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>

        {/* Action Buttons - Shows on Hover */}
        <div className={`absolute bottom-3 left-3 right-3 transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button className="bg-gray-800/80 hover:bg-gray-700 text-white p-2 rounded-full transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
              <button className="bg-gray-800/80 hover:bg-gray-700 text-white p-2 rounded-full transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
            </div>
            <button className="bg-gray-800/80 hover:bg-gray-700 text-white p-2 rounded-full transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Movie Info */}
      <div className="mt-3 space-y-1">
        <h3 className="text-white font-semibold text-lg leading-tight line-clamp-2 group-hover:text-red-400 transition-colors">
          {movie.name}
        </h3>
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <span>{movie.year}</span>
          <span>•</span>
          <span>{movie.time || 'N/A'}</span>
          <span>•</span>
          <span className="bg-gray-700 px-2 py-1 rounded text-xs">{movie.quality}</span>
        </div>
      </div>

      {/* Hover Card Extension */}
      {isHovered && (
        <div className="absolute top-0 left-0 w-full bg-gray-800 rounded-lg shadow-2xl border border-gray-700 z-10 transform scale-105 transition-transform duration-300">
          <div className="p-4 space-y-3">
            <img
              src={getImageUrl(movie.poster_url || movie.thumb_url)}
              alt={movie.name}
              className="w-full h-48 object-cover rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = createPlaceholderImage(300, 200, 'No Image')
              }}
            />
            <div>
              <h3 className="text-white font-semibold text-lg">{movie.name}</h3>
              <div className="flex items-center space-x-2 text-sm text-gray-400 mt-1">
                <span className="flex items-center space-x-1">
                  <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span>{getRating()}</span>
                </span>
                <span>•</span>
                <span>{movie.year}</span>
                <span>•</span>
                <span>{movie.time || 'N/A'}</span>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {movie.category && movie.category.length > 0 ? (
                  movie.category.slice(0, 3).map((cat) => (
                    <span key={cat.id} className="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">
                      {cat.name}
                    </span>
                  ))
                ) : (
                  <span className="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">
                    Phim
                  </span>
                )}
              </div>
            </div>
            <div className="flex space-x-2">
              <Link
                to={`/movie/${movie.slug}`}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors flex-1 text-center"
              >
                Xem Phim
              </Link>
              <button className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MovieCard
