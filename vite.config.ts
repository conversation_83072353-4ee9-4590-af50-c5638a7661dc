import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'https://phimapi.com',
        changeOrigin: true,
        secure: true,
        timeout: 30000,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('Proxy error:', err.message);
            console.log('Failed URL:', req.url);

            // Send fallback response on proxy error
            if (!res.headersSent) {
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({
                status: false,
                msg: 'API temporarily unavailable. Using fallback data.',
                error: 'PROXY_ERROR'
              }));
            }
          });

          proxy.on('proxyReq', (proxyReq, req, _res) => {
            // Add headers to prevent connection issues
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            proxyReq.setHeader('Accept', 'application/json, text/plain, */*');
            proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.9,vi;q=0.8');
            proxyReq.setHeader('Cache-Control', 'no-cache');
            proxyReq.setHeader('Connection', 'keep-alive');

            console.log('Sending Request to the Target:', req.method, req.url);
          });

          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });

          proxy.on('proxyReqError', (err, req, res) => {
            console.log('Proxy request error:', err.message);
            if (!res.headersSent) {
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({
                status: false,
                msg: 'Connection failed. Using fallback data.',
                error: 'CONNECTION_ERROR'
              }));
            }
          });
        },
      }
    }
  }
})
