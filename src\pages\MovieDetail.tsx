import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { MovieDetail as MovieDetailType } from '../types/movie'
import { movieApi } from '../services/movieApi'
import MoviePlayer from '../components/MoviePlayer'
import Header from '../components/Header'
import Footer from '../components/Footer'

const MovieDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>()
  const navigate = useNavigate()
  const [movie, setMovie] = useState<MovieDetailType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchMovieDetail = async () => {
      if (!slug) {
        setError('Không tìm thấy thông tin phim')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)
        const data = await movieApi.getMovieDetail(slug)
        
        if (data.status && data.movie) {
          setMovie(data)
        } else {
          setError(data.msg || 'Không thể tải thông tin phim')
        }
      } catch (err) {
        console.error('Error fetching movie detail:', err)
        setError('Có lỗi xảy ra khi tải thông tin phim')
      } finally {
        setLoading(false)
      }
    }

    fetchMovieDetail()
  }, [slug])

  const handleGoBack = () => {
    navigate(-1)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-red-500 mx-auto mb-4"></div>
            <p className="text-lg">Đang tải thông tin phim...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (error || !movie) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center text-white max-w-md mx-auto px-4">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold mb-4">Không thể tải phim</h2>
            <p className="text-gray-400 mb-6">{error}</p>
            <div className="space-x-4">
              <button
                onClick={handleGoBack}
                className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                Quay lại
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Thử lại
              </button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Header />
      
      <main className="px-4 md:px-8 lg:px-16 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <button
            onClick={handleGoBack}
            className="flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Quay lại
          </button>
        </div>

        {/* Movie Player */}
        <MoviePlayer movie={movie.movie} />
      </main>
      
      <Footer />
    </div>
  )
}

export default MovieDetail
