// Environment configuration
export const ENV = {
  // API Configuration
  MOVIE_API_BASE_URL: import.meta.env.VITE_MOVIE_API_BASE_URL || 'https://ophim1.com',
  MOVIE_API_CDN_URL: import.meta.env.VITE_MOVIE_API_CDN_URL || 'https://img.phimapi.com',

  // API Endpoints
  API_NEW_MOVIES: import.meta.env.VITE_API_NEW_MOVIES || '/danh-sach/phim-moi-cap-nhat',
  API_MOVIE_DETAIL: import.meta.env.VITE_API_MOVIE_DETAIL || '/phim',
  API_SEARCH: import.meta.env.VITE_API_SEARCH || '/v1/api/tim-kiem',
  API_CATEGORY: import.meta.env.VITE_API_CATEGORY || '/v1/api/the-loai',
  API_COUNTRY: import.meta.env.VITE_API_COUNTRY || '/v1/api/quoc-gia',
  API_YEAR: import.meta.env.VITE_API_YEAR || '/v1/api/nam',
  API_LIST: import.meta.env.VITE_API_LIST || '/v1/api/danh-sach',

  // Development mode
  IS_DEV: import.meta.env.DEV,
  IS_PROD: import.meta.env.PROD,
} as const

// Validate required environment variables
export const validateEnv = () => {
  const requiredVars = [
    'VITE_MOVIE_API_BASE_URL',
    'VITE_MOVIE_API_CDN_URL'
  ]

  const missing = requiredVars.filter(varName => !import.meta.env[varName])

  if (missing.length > 0 && ENV.IS_PROD) {
    console.warn('Missing environment variables:', missing)
    console.warn('Using default values. Consider setting these in .env file.')
  }

  if (ENV.IS_DEV) {
    console.log('🔧 Environment Configuration:')
    console.log('- API Base URL:', ENV.MOVIE_API_BASE_URL)
    console.log('- CDN URL:', ENV.MOVIE_API_CDN_URL)
    console.log('- Mode:', ENV.IS_DEV ? 'Development' : 'Production')
  }
}

// Create placeholder image as data URL
export const createPlaceholderImage = (width: number = 300, height: number = 450, text: string = 'No Image'): string => {
  const svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="${width}" height="${height}" fill="#374151"/>
<text x="${width/2}" y="${height/2}" font-family="Arial" font-size="16" fill="#9CA3AF" text-anchor="middle" dominant-baseline="central">${text}</text>
</svg>`
  return `data:image/svg+xml;base64,${btoa(svg)}`
}

// Helper functions
export const getImageUrl = (url: string): string => {
  if (!url) return createPlaceholderImage(300, 450, 'No Image')
  if (url.startsWith('http')) return url
  return `${ENV.MOVIE_API_CDN_URL}/${url}`
}

export const getBackdropUrl = (url: string): string => {
  if (!url) return 'https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=1920&h=1080&fit=crop'
  if (url.startsWith('http')) return url
  return `${ENV.MOVIE_API_CDN_URL}/${url}`
}

// Initialize environment validation
validateEnv()
