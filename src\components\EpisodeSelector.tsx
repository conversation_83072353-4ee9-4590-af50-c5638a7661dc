import React from 'react'
import { Episode, EpisodeData } from '../types/movie'

interface EpisodeSelectorProps {
  episodes: Episode[]
  currentEpisode?: EpisodeData
  onEpisodeSelect: (episode: EpisodeData) => void
  className?: string
}

const EpisodeSelector: React.FC<EpisodeSelectorProps> = ({
  episodes,
  currentEpisode,
  onEpisodeSelect,
  className = ''
}) => {
  if (!episodes || episodes.length === 0) {
    return null
  }

  return (
    <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
      <h3 className="text-white text-xl font-semibold mb-4">Danh sách tập phim</h3>

      {episodes.map((server, serverIndex) => (
        <div key={serverIndex} className="mb-6 last:mb-0">
          <h4 className="text-gray-300 text-lg font-medium mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
            </svg>
            {server.server_name}
          </h4>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-2">
            {server.server_data.map((episode, episodeIndex) => {
              const isActive = currentEpisode?.slug === episode.slug

              return (
                <button
                  key={episodeIndex}
                  onClick={() => onEpisodeSelect(episode)}
                  className={`
                    px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
                    ${isActive
                      ? 'bg-red-600 text-white shadow-lg transform scale-105'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
                    }
                    focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-800
                  `}
                  title={`Tập ${episode.name}`}
                >
                  {episode.name}
                </button>
              )
            })}
          </div>
        </div>
      ))}

      {/* Episode Info */}
      {currentEpisode && (
        <div className="mt-6 p-4 bg-gray-700 rounded-lg">
          <h5 className="text-white font-medium mb-2">Đang phát:</h5>
          <p className="text-gray-300 text-sm">
            {currentEpisode.name}
            {currentEpisode.filename && (
              <span className="text-gray-400 ml-2 block mt-1 text-xs">
                {currentEpisode.filename}
              </span>
            )}
          </p>

          {/* Episode Navigation */}
          <div className="mt-3 flex items-center justify-between">
            <div className="text-xs text-gray-400">
              Chọn tập khác để xem tiếp
            </div>
            <div className="flex items-center space-x-2 text-xs">
              <span className="text-gray-400">Chất lượng:</span>
              <span className="px-2 py-1 bg-red-600 text-white rounded text-xs">
                {currentEpisode.filename?.includes('1080p') ? 'FHD' : 'HD'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EpisodeSelector
