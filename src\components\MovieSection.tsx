import { useState, useEffect } from 'react'
import MovieCard from './MovieCard'
import type { Movie } from '../types/movie'
import movieApi from '../services/movieApi'

interface MovieSectionProps {
  title: string
  type?: 'trending' | 'popular' | 'new-releases' | 'action' | 'comedy' | 'tv-shows'
}

const MovieSection = ({ title, type = 'trending' }: MovieSectionProps) => {
  const [movies, setMovies] = useState<Movie[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  // Fallback movies data
  const getFallbackMovies = (): Movie[] => [
    {
      _id: 'fallback-1',
      name: 'Spider-Man: No Way Home',
      slug: 'spider-man-no-way-home',
      origin_name: 'Spider-Man: No Way Home',
      poster_url: 'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop',
      thumb_url: 'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop',
      year: 2021,
      content: 'Peter Parker được tiết lộ danh tính Spider-Man và không thể tách biệt cuộc sống bình thường với rủi ro cao của việc trở thành siêu anh hùng.',
      type: 'single',
      status: 'completed',
      time: '148 phút',
      episode_current: 'Full',
      episode_total: '1',
      quality: 'HD',
      lang: 'Vietsub',
      notify: '',
      showtimes: '',
      trailer_url: '',
      category: [
        { id: '1', name: 'Hành Động', slug: 'hanh-dong' },
        { id: '2', name: 'Phiêu Lưu', slug: 'phieu-luu' }
      ],
      country: [{ id: '1', name: 'Mỹ', slug: 'my' }],
      actor: ['Tom Holland', 'Zendaya', 'Benedict Cumberbatch'],
      director: ['Jon Watts'],
      chieurap: false,
      tmdb: { type: 'movie', id: '634649', season: null, vote_average: 8.4, vote_count: 15000 },
      imdb: { id: 'tt10872600' },
      created: { time: '2024-01-01T00:00:00.000Z' },
      modified: { time: '2024-01-01T00:00:00.000Z' }
    },
    {
      _id: 'fallback-2',
      name: 'Dune',
      slug: 'dune',
      origin_name: 'Dune',
      poster_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=450&fit=crop',
      thumb_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=450&fit=crop',
      year: 2021,
      content: 'Paul Atreides, một chàng trai thông minh và tài năng sinh ra để làm những điều vĩ đại vượt xa sự hiểu biết của anh.',
      type: 'single',
      status: 'completed',
      time: '155 phút',
      episode_current: 'Full',
      episode_total: '1',
      quality: 'HD',
      lang: 'Vietsub',
      notify: '',
      showtimes: '',
      trailer_url: '',
      category: [
        { id: '1', name: 'Khoa Học Viễn Tưởng', slug: 'khoa-hoc-vien-tuong' },
        { id: '2', name: 'Phiêu Lưu', slug: 'phieu-luu' }
      ],
      country: [{ id: '1', name: 'Mỹ', slug: 'my' }],
      actor: ['Timothée Chalamet', 'Rebecca Ferguson', 'Oscar Isaac'],
      director: ['Denis Villeneuve'],
      chieurap: false,
      tmdb: { type: 'movie', id: '438631', season: null, vote_average: 8.0, vote_count: 12000 },
      imdb: { id: 'tt1160419' },
      created: { time: '2024-01-01T00:00:00.000Z' },
      modified: { time: '2024-01-01T00:00:00.000Z' }
    },
    {
      _id: 'fallback-3',
      name: 'The Batman',
      slug: 'the-batman',
      origin_name: 'The Batman',
      poster_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop',
      thumb_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop',
      year: 2022,
      content: 'Khi kẻ giết người hàng loạt nhắm vào các nhân vật chính trị quan trọng ở Gotham, Batman phải điều tra những manh mối bí ẩn.',
      type: 'single',
      status: 'completed',
      time: '176 phút',
      episode_current: 'Full',
      episode_total: '1',
      quality: 'HD',
      lang: 'Vietsub',
      notify: '',
      showtimes: '',
      trailer_url: '',
      category: [
        { id: '1', name: 'Hành Động', slug: 'hanh-dong' },
        { id: '2', name: 'Hình Sự', slug: 'hinh-su' }
      ],
      country: [{ id: '1', name: 'Mỹ', slug: 'my' }],
      actor: ['Robert Pattinson', 'Zoë Kravitz', 'Paul Dano'],
      director: ['Matt Reeves'],
      chieurap: false,
      tmdb: { type: 'movie', id: '414906', season: null, vote_average: 7.8, vote_count: 10000 },
      imdb: { id: 'tt1877830' },
      created: { time: '2024-01-01T00:00:00.000Z' },
      modified: { time: '2024-01-01T00:00:00.000Z' }
    },
    {
      _id: 'fallback-4',
      name: 'Top Gun: Maverick',
      slug: 'top-gun-maverick',
      origin_name: 'Top Gun: Maverick',
      poster_url: 'https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?w=300&h=450&fit=crop',
      thumb_url: 'https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?w=300&h=450&fit=crop',
      year: 2022,
      content: 'Sau hơn ba mười năm phục vụ như một trong những phi công hàng đầu của Hải quân, Pete "Maverick" Mitchell đang ở nơi anh thuộc về.',
      type: 'single',
      status: 'completed',
      time: '131 phút',
      episode_current: 'Full',
      episode_total: '1',
      quality: 'HD',
      lang: 'Vietsub',
      notify: '',
      showtimes: '',
      trailer_url: '',
      category: [
        { id: '1', name: 'Hành Động', slug: 'hanh-dong' },
        { id: '2', name: 'Chính Kịch', slug: 'chinh-kich' }
      ],
      country: [{ id: '1', name: 'Mỹ', slug: 'my' }],
      actor: ['Tom Cruise', 'Miles Teller', 'Jennifer Connelly'],
      director: ['Joseph Kosinski'],
      chieurap: false,
      tmdb: { type: 'movie', id: '361743', season: null, vote_average: 8.3, vote_count: 8000 },
      imdb: { id: 'tt1745960' },
      created: { time: '2024-01-01T00:00:00.000Z' },
      modified: { time: '2024-01-01T00:00:00.000Z' }
    },
    {
      _id: 'fallback-5',
      name: 'Avatar: The Way of Water',
      slug: 'avatar-the-way-of-water',
      origin_name: 'Avatar: The Way of Water',
      poster_url: 'https://images.unsplash.com/photo-1544198365-f5d60b6d8190?w=300&h=450&fit=crop',
      thumb_url: 'https://images.unsplash.com/photo-1544198365-f5d60b6d8190?w=300&h=450&fit=crop',
      year: 2022,
      content: 'Jake Sully sống với gia đình mới được thành lập trên hành tinh Pandora. Khi một mối đe dọa quen thuộc trở lại để hoàn thành những gì đã bắt đầu trước đây.',
      type: 'single',
      status: 'completed',
      time: '192 phút',
      episode_current: 'Full',
      episode_total: '1',
      quality: 'HD',
      lang: 'Vietsub',
      notify: '',
      showtimes: '',
      trailer_url: '',
      category: [
        { id: '1', name: 'Khoa Học Viễn Tưởng', slug: 'khoa-hoc-vien-tuong' },
        { id: '2', name: 'Phiêu Lưu', slug: 'phieu-luu' }
      ],
      country: [{ id: '1', name: 'Mỹ', slug: 'my' }],
      actor: ['Sam Worthington', 'Zoe Saldana', 'Sigourney Weaver'],
      director: ['James Cameron'],
      chieurap: false,
      tmdb: { type: 'movie', id: '76600', season: null, vote_average: 7.6, vote_count: 6000 },
      imdb: { id: 'tt1630029' },
      created: { time: '2024-01-01T00:00:00.000Z' },
      modified: { time: '2024-01-01T00:00:00.000Z' }
    }
  ]

  // Fetch movies based on type
  useEffect(() => {
    const fetchMovies = async () => {
      try {
        setLoading(true)
        setError(null)

        let response
        switch (type) {
          case 'trending':
            response = await movieApi.getTrendingMovies(20)
            break
          case 'popular':
            response = await movieApi.getPopularMovies(20)
            break
          case 'new-releases':
            response = await movieApi.getNewMovies(1)
            break
          case 'action':
            response = await movieApi.getActionMovies(20)
            break
          case 'comedy':
            response = await movieApi.getComedyMovies(20)
            break
          case 'tv-shows':
            response = await movieApi.getTVShows(20)
            break
          default:
            response = await movieApi.getTrendingMovies(20)
        }

        console.log('MovieSection API Response:', response) // Debug log

        if (response.status && response.data && response.data.items) {
          setMovies(response.data.items)
        } else if ((response as any).items) {
          // Handle different response structure
          setMovies((response as any).items)
        } else {
          console.warn('No movies found in response:', response)
          // Set fallback movies data
          setMovies(getFallbackMovies())
        }
      } catch (err) {
        console.error('Error fetching movies:', err)
        // Set fallback movies data on error
        setMovies(getFallbackMovies())
      } finally {
        setLoading(false)
      }
    }

    fetchMovies()
  }, [type])

  const handleScroll = (direction: 'left' | 'right') => {
    const container = document.getElementById(`movie-container-${title.replace(/\s+/g, '-')}`)
    if (!container) return

    const scrollAmount = 320 // Width of one movie card + gap
    const newPosition = direction === 'left'
      ? Math.max(0, scrollPosition - scrollAmount)
      : scrollPosition + scrollAmount

    container.scrollTo({
      left: newPosition,
      behavior: 'smooth'
    })

    setScrollPosition(newPosition)
    setCanScrollLeft(newPosition > 0)
    setCanScrollRight(newPosition < container.scrollWidth - container.clientWidth)
  }

  return (
    <section className="space-y-4">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl md:text-3xl font-bold text-white">{title}</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => handleScroll('left')}
            disabled={!canScrollLeft}
            className={`p-2 rounded-full transition-colors ${
              canScrollLeft
                ? 'bg-gray-800 hover:bg-gray-700 text-white'
                : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={() => handleScroll('right')}
            disabled={!canScrollRight}
            className={`p-2 rounded-full transition-colors ${
              canScrollRight
                ? 'bg-gray-800 hover:bg-gray-700 text-white'
                : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Movies Container */}
      <div className="relative">
        {loading && (
          <div className="flex space-x-4">
            {[...Array(7)].map((_, index) => (
              <div key={index} className="flex-shrink-0 w-72">
                <div className="bg-gray-800 rounded-lg h-96 animate-pulse"></div>
                <div className="mt-3 space-y-2">
                  <div className="bg-gray-800 h-4 rounded animate-pulse"></div>
                  <div className="bg-gray-800 h-3 rounded w-2/3 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {error && (
          <div className="text-center py-8">
            <p className="text-red-400 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Thử lại
            </button>
          </div>
        )}

        {!loading && !error && movies.length > 0 && (
          <div
            id={`movie-container-${title.replace(/\s+/g, '-')}`}
            className="flex space-x-4 overflow-x-auto scrollbar-hide scroll-smooth"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {movies.map((movie) => (
              <MovieCard key={movie._id} movie={movie} />
            ))}
          </div>
        )}

        {!loading && !error && movies.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-400">Không có phim nào để hiển thị</p>
          </div>
        )}
      </div>
    </section>
  )
}

export default MovieSection
