// Types for KKPhim API
export interface Movie {
  _id: string
  name: string
  slug: string
  origin_name: string
  poster_url: string
  thumb_url: string
  year: number
  content: string
  type: string
  status: string
  time: string
  episode_current: string
  episode_total: string
  quality: string
  lang: string
  notify: string
  showtimes: string
  trailer_url: string
  is_copyright?: boolean
  sub_docquyen?: boolean
  chieurap: boolean
  view?: number
  category: Category[]
  country: Country[]
  actor: string[]
  director: string[]
  tmdb: {
    type: string
    id: string
    season: number | null
    vote_average: number
    vote_count: number
  }
  imdb: {
    id: string | null
  }
  created: {
    time: string
  }
  modified: {
    time: string
  }
}

export interface Category {
  id: string
  name: string
  slug: string
}

export interface Country {
  id: string
  name: string
  slug: string
}

export interface ApiResponse {
  status: boolean
  msg: string
  data: {
    seoOnPage: {
      og_type: string
      titleHead: string
      descriptionHead: string
      og_image: string[]
      og_url: string
    }
    breadCrumb: Array<{
      name: string
      slug?: string
      isCurrent?: boolean
    }>
    titlePage: string
    items: Movie[]
    params: {
      type_slug: string
      filterCategory: string[]
      filterCountry: string[]
      filterYear: string
      filterType: string
      sortField: string
      sortType: string
      pagination: {
        totalItems: number
        totalItemsPerPage: number
        currentPage: number
        totalPages: number
      }
    }
    type_list: string
    APP_DOMAIN_FRONTEND: string
    APP_DOMAIN_CDN_IMAGE: string
  }
}

export interface MovieDetail {
  status: boolean
  msg: string
  movie: Movie & {
    episodes: Episode[]
  }
}

export interface Episode {
  server_name: string
  server_data: EpisodeData[]
}

export interface EpisodeData {
  name: string
  slug: string
  filename: string
  link_embed: string
  link_m3u8: string
}

export interface PlayerProps {
  movie: Movie & { episodes: Episode[] }
  currentEpisode?: EpisodeData
  onEpisodeChange?: (episode: EpisodeData) => void
}

import { ENV } from '../config/env'

// API endpoints configuration from environment variables
export const API_ENDPOINTS = {
  BASE_URL: ENV.MOVIE_API_BASE_URL,
  CDN_URL: ENV.MOVIE_API_CDN_URL,
  NEW_MOVIES: ENV.API_NEW_MOVIES,
  MOVIE_DETAIL: ENV.API_MOVIE_DETAIL,
  SEARCH: ENV.API_SEARCH,
  CATEGORY: ENV.API_CATEGORY,
  COUNTRY: ENV.API_COUNTRY,
  YEAR: ENV.API_YEAR,
  LIST: ENV.API_LIST
} as const

export type MovieType = 'phim-bo' | 'phim-le' | 'tv-shows' | 'hoat-hinh'
export type SortField = 'modified.time' | '_id' | 'year'
export type SortType = 'desc' | 'asc'
