import React, { useState, useEffect } from 'react'

interface ApiStatusProps {
  className?: string
}

const ApiStatus: React.FC<ApiStatusProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<'checking' | 'online' | 'offline' | 'fallback'>('checking')
  const [lastCheck, setLastCheck] = useState<Date | null>(null)

  const checkApiStatus = async () => {
    try {
      setStatus('checking')
      const response = await fetch('/api/danh-sach/phim-moi-cap-nhat-v3?page=1', {
        method: 'HEAD', // Only check headers, don't download content
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })
      
      if (response.ok) {
        setStatus('online')
      } else if (response.status === 429) {
        setStatus('fallback') // Rate limited, using fallback
      } else {
        setStatus('offline')
      }
    } catch (error) {
      console.log('API check failed:', error)
      setStatus('fallback')
    } finally {
      setLastCheck(new Date())
    }
  }

  useEffect(() => {
    checkApiStatus()
    
    // Check every 30 seconds
    const interval = setInterval(checkApiStatus, 30000)
    
    return () => clearInterval(interval)
  }, [])

  const getStatusInfo = () => {
    switch (status) {
      case 'checking':
        return {
          color: 'text-yellow-500',
          bg: 'bg-yellow-500/10',
          icon: '⏳',
          text: 'Đang kiểm tra...',
          description: 'Kiểm tra kết nối API'
        }
      case 'online':
        return {
          color: 'text-green-500',
          bg: 'bg-green-500/10',
          icon: '🟢',
          text: 'API Online',
          description: 'Dữ liệu thời gian thực'
        }
      case 'offline':
        return {
          color: 'text-red-500',
          bg: 'bg-red-500/10',
          icon: '🔴',
          text: 'API Offline',
          description: 'Sử dụng dữ liệu mẫu'
        }
      case 'fallback':
        return {
          color: 'text-orange-500',
          bg: 'bg-orange-500/10',
          icon: '🟡',
          text: 'Chế độ dự phòng',
          description: 'API bị giới hạn, dùng dữ liệu mẫu'
        }
    }
  }

  const statusInfo = getStatusInfo()

  return (
    <div className={`${statusInfo.bg} rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <span className="text-lg">{statusInfo.icon}</span>
        <div className="flex-1 min-w-0">
          <p className={`text-sm font-medium ${statusInfo.color}`}>
            {statusInfo.text}
          </p>
          <p className="text-xs text-gray-400">
            {statusInfo.description}
          </p>
          {lastCheck && (
            <p className="text-xs text-gray-500 mt-1">
              Cập nhật: {lastCheck.toLocaleTimeString('vi-VN')}
            </p>
          )}
        </div>
        <button
          onClick={checkApiStatus}
          disabled={status === 'checking'}
          className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
          title="Kiểm tra lại"
        >
          <svg 
            className={`w-4 h-4 ${status === 'checking' ? 'animate-spin' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default ApiStatus
