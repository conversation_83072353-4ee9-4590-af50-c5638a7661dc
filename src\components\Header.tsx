import { useState } from 'react'
import { Link } from 'react-router-dom'
import ApiStatus from './ApiStatus'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-gray-900/95 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50">
      <div className="px-4 md:px-8 lg:px-16 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-8">
            <Link to="/" className="text-2xl font-bold text-red-600 hover:text-red-500 transition-colors">
              MOVIEFLIX
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8">
              <Link to="/" className="text-white hover:text-red-500 transition-colors">Home</Link>
              <a href="#" className="text-gray-300 hover:text-red-500 transition-colors">Movies</a>
              <a href="#" className="text-gray-300 hover:text-red-500 transition-colors">TV Shows</a>
              <a href="#" className="text-gray-300 hover:text-red-500 transition-colors">My List</a>
              <Link to="/test-player" className="text-gray-300 hover:text-red-500 transition-colors">Test Player</Link>
            </nav>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="hidden md:flex items-center bg-gray-800 rounded-lg px-3 py-2">
              <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                type="text"
                placeholder="Search movies..."
                className="bg-transparent text-white placeholder-gray-400 outline-none w-48"
              />
            </div>

            {/* API Status - Desktop */}
            <div className="hidden lg:block">
              <ApiStatus className="max-w-xs" />
            </div>

            {/* Notifications */}
            <button className="text-gray-300 hover:text-white transition-colors">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6a2 2 0 012 2v9a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2z" />
              </svg>
            </button>

            {/* Profile */}
            <div className="flex items-center space-x-2">
              <img
                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
                alt="Profile"
                className="w-8 h-8 rounded-full"
              />
              <svg className="w-4 h-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>

            {/* Mobile menu button */}
            <button
              className="md:hidden text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-gray-800 pt-4">
            <div className="flex flex-col space-y-4">
              <a href="#" className="text-white hover:text-red-500 transition-colors">Home</a>
              <a href="#" className="text-gray-300 hover:text-red-500 transition-colors">Movies</a>
              <a href="#" className="text-gray-300 hover:text-red-500 transition-colors">TV Shows</a>
              <a href="#" className="text-gray-300 hover:text-red-500 transition-colors">My List</a>

              {/* Mobile Search */}
              <div className="flex items-center bg-gray-800 rounded-lg px-3 py-2 mt-4">
                <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <input
                  type="text"
                  placeholder="Search movies..."
                  className="bg-transparent text-white placeholder-gray-400 outline-none w-full"
                />
              </div>

              {/* API Status - Mobile */}
              <div className="mt-4">
                <ApiStatus />
              </div>
            </div>
          </nav>
        )}
      </div>
    </header>
  )
}

export default Header
