import React from 'react'
import { <PERSON> } from 'react-router-dom'
import VideoPlayer from '../components/VideoPlayer'
import { EpisodeData } from '../types/movie'

const TestPlayer: React.FC = () => {
  // Test episode data
  const testEpisode: EpisodeData = {
    name: "Tập 1",
    slug: "tap-1",
    filename: "test-video.mp4",
    link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
    link_m3u8: ""
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <Link 
            to="/"
            className="flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Quay lại trang chủ
          </Link>
        </div>

        <h1 className="text-white text-3xl font-bold mb-8">Test Video Player</h1>
        
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h2 className="text-white text-xl font-semibold mb-4">Big Buck Bunny (Test Video)</h2>
          <VideoPlayer
            episode={testEpisode}
            autoPlay={false}
            className="aspect-video w-full"
          />
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-white text-lg font-semibold mb-4">Thông tin test</h3>
          <div className="text-gray-300 space-y-2">
            <p><strong>Video:</strong> Big Buck Bunny (Sample Video)</p>
            <p><strong>Định dạng:</strong> MP4</p>
            <p><strong>Nguồn:</strong> Google Cloud Storage</p>
            <p><strong>Tính năng:</strong> Play/Pause, Seek, Volume, Fullscreen</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TestPlayer
